import React from 'react';

interface TemplateProps {
  personalInfo: any;
  experience: any[];
  education: any[];
  skills: any[];
  pdfComponents: any;
}

export function ClassicTemplate({ personalInfo, experience, education, skills, pdfComponents }: TemplateProps) {
  const { Document, Page, Text, View, StyleSheet } = pdfComponents;

  // Define styles for the Classic template
  const classicStyles = StyleSheet.create({
    page: {
      flexDirection: 'column',
      backgroundColor: '#ffffff',
      padding: 50,
      fontFamily: 'Times-Roman',
      fontSize: 11,
      lineHeight: 1.4,
    },
    header: {
      alignItems: 'center',
      marginBottom: 25,
      borderBottomWidth: 1,
      borderBottomColor: '#000000',
      paddingBottom: 15,
    },
    name: {
      fontSize: 24,
      fontWeight: 'bold',
      color: '#000000',
      marginBottom: 8,
      textAlign: 'center',
    },
    contactInfo: {
      flexDirection: 'row',
      justifyContent: 'center',
      fontSize: 10,
      color: '#000000',
      marginBottom: 8,
    },
    contactItem: {
      marginHorizontal: 8,
    },
    summary: {
      fontSize: 11,
      lineHeight: 1.4,
      color: '#000000',
      textAlign: 'center',
      marginTop: 10,
      fontStyle: 'italic',
    },
    section: {
      marginBottom: 25,
      marginTop: 15,
    },
    sectionTitle: {
      fontSize: 14,
      fontWeight: 'bold',
      color: '#000000',
      marginBottom: 12,
      marginTop: 8,
      textAlign: 'center',
      textTransform: 'uppercase',
      letterSpacing: 1,
    },
    text: {
      fontSize: 11,
      lineHeight: 1.4,
      color: '#000000',
      marginBottom: 8,
    },
  experienceItem: {
    marginBottom: 15,
  },
  experienceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 6,
    alignItems: 'flex-start',
  },
  jobHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  jobTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 2,
  },
  company: {
    fontSize: 11,
    color: '#000000',
    fontStyle: 'italic',
    marginBottom: 2,
  },
  dateLocation: {
    fontSize: 10,
    color: '#000000',
    textAlign: 'right',
  },
  date: {
    fontSize: 10,
    color: '#000000',
    textAlign: 'right',
  },
  description: {
    fontSize: 11,
    lineHeight: 1.3,
    color: '#000000',
    marginTop: 3,
    marginLeft: 10,
  },
  bulletPoint: {
    fontSize: 11,
    lineHeight: 1.3,
    color: '#000000',
    marginBottom: 3,
    marginLeft: 15,
  },
  skillsContainer: {
    flexDirection: 'column',
  },
  skillCategory: {
    marginBottom: 8,
  },
  skillCategoryTitle: {
    fontSize: 11,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 3,
  },
  skillsList: {
    fontSize: 11,
    color: '#000000',
    lineHeight: 1.2,
  },
});

  return (
    <Document>
      <Page size="A4" style={classicStyles.page}>
        {/* Header */}
        <View style={classicStyles.header}>
          <Text style={classicStyles.name}>
            {personalInfo.fullName || 'Your Name'}
          </Text>
          <View style={classicStyles.contactInfo}>
            <Text style={classicStyles.contactItem}>{personalInfo.email}</Text>
            <Text style={classicStyles.contactItem}>•</Text>
            <Text style={classicStyles.contactItem}>{personalInfo.phone}</Text>
            <Text style={classicStyles.contactItem}>•</Text>
            <Text style={classicStyles.contactItem}>{personalInfo.location}</Text>
          </View>
          {personalInfo.linkedin && (
            <Text style={classicStyles.contactInfo}>{personalInfo.linkedin}</Text>
          )}
        </View>

        {/* Summary */}
        {personalInfo.summary && (
          <View style={classicStyles.section}>
            <Text style={classicStyles.sectionTitle}>PROFESSIONAL SUMMARY</Text>
            <Text style={classicStyles.text}>{personalInfo.summary}</Text>
          </View>
        )}

        {/* Experience */}
        {experience && experience.length > 0 && (
          <View style={classicStyles.section}>
            <Text style={classicStyles.sectionTitle}>PROFESSIONAL EXPERIENCE</Text>
            {experience.map((exp: any, index: number) => (
              <View key={index} style={classicStyles.experienceItem}>
                <View style={classicStyles.experienceHeader}>
                  <View style={{ flex: 1 }}>
                    <Text style={classicStyles.jobTitle}>{exp.jobTitle}</Text>
                    <Text style={classicStyles.company}>{exp.company}, {exp.location}</Text>
                  </View>
                  <Text style={classicStyles.date}>
                    {exp.startDate} - {exp.current ? 'Present' : exp.endDate}
                  </Text>
                </View>
                {exp.description && exp.description.map((desc: string, descIndex: number) => (
                  <Text key={descIndex} style={classicStyles.bulletPoint}>
                    • {desc}
                  </Text>
                ))}
              </View>
            ))}
          </View>
        )}

        {/* Education */}
        {education && education.length > 0 && (
          <View style={classicStyles.section}>
            <Text style={classicStyles.sectionTitle}>EDUCATION</Text>
            {education.map((edu: any, index: number) => (
              <View key={index} style={classicStyles.experienceItem}>
                <View style={classicStyles.experienceHeader}>
                  <View style={{ flex: 1 }}>
                    <Text style={classicStyles.jobTitle}>{edu.degree}</Text>
                    <Text style={classicStyles.company}>{edu.institution}, {edu.location}</Text>
                  </View>
                  <Text style={classicStyles.dateLocation}>{edu.graduationDate}</Text>
                </View>
                {edu.gpa && (
                  <Text style={classicStyles.text}>GPA: {edu.gpa}</Text>
                )}
              </View>
            ))}
          </View>
        )}

        {/* Skills */}
        {skills && skills.length > 0 && (
          <View style={classicStyles.section}>
            <Text style={classicStyles.sectionTitle}>CORE COMPETENCIES</Text>
            <Text style={classicStyles.text}>
              {skills.map((skill: any) => skill.name).join(' • ')}
            </Text>
          </View>
        )}
      </Page>
    </Document>
  );
}
