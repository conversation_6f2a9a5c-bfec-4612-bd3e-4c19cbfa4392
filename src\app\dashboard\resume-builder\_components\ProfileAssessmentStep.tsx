"use client";

import { useState, useEffect } from "react";
import { useGlobalStore, UserProfile } from "@/store/useGlobalStore";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Badge } from "@/components/ui/badge";
import { User, Briefcase, Building, Target, CheckCircle } from "lucide-react";
import { cn } from "@/lib/utils";

const careerLevels = [
  {
    value: "entry-level" as const,
    label: "Entry Level",
    description: "0-2 years of experience",
    icon: User,
    yearsOfExperience: 1,
    hasWorkExperience: false,
  },
  {
    value: "mid-career" as const,
    label: "Mid Career",
    description: "3-7 years of experience",
    icon: Briefcase,
    yearsOfExperience: 5,
    hasWorkExperience: true,
  },
  {
    value: "senior" as const,
    label: "Senior",
    description: "8-15 years of experience",
    icon: Building,
    yearsOfExperience: 10,
    hasWorkExperience: true,
  },
  {
    value: "executive" as const,
    label: "Executive",
    description: "15+ years of experience",
    icon: Target,
    yearsOfExperience: 15,
    hasWorkExperience: true,
  },
];

const employmentStatuses = [
  {
    value: "student" as const,
    label: "Student",
    description: "Currently studying",
  },
  {
    value: "employed" as const,
    label: "Employed",
    description: "Currently working",
  },
  {
    value: "unemployed" as const,
    label: "Unemployed",
    description: "Seeking employment",
  },
  {
    value: "career-change" as const,
    label: "Career Change",
    description: "Transitioning careers",
  },
];

const commonIndustries = [
  "Technology",
  "Finance",
  "Healthcare",
  "Education",
  "Marketing",
  "Sales",
  "Consulting",
  "Legal",
  "Government",
  "Non-profit",
  "Manufacturing",
  "Retail",
  "Media",
  "Design",
  "Arts",
  "Research",
  "Startups",
  "Real Estate",
  "Transportation",
  "Other",
];

export function ProfileAssessmentStep() {
  const { resumeBuilder, setUserProfile } = useGlobalStore();
  const { userProfile } = resumeBuilder;

  const [formData, setFormData] = useState<Partial<UserProfile>>(
    userProfile || {
      careerLevel: undefined,
      employmentStatus: undefined,
      industry: "",
      targetRole: "",
      yearsOfExperience: 0,
      hasWorkExperience: false,
      profileScore: 0,
    }
  );

  const handleCareerLevelChange = (value: UserProfile["careerLevel"]) => {
    const careerLevelData = careerLevels.find((level) => level.value === value);
    if (!careerLevelData) return;

    const updatedData = {
      ...formData,
      careerLevel: value,
      yearsOfExperience: careerLevelData.yearsOfExperience,
      hasWorkExperience: careerLevelData.hasWorkExperience,
    };

    setFormData(updatedData);
    updateProfileInStore(updatedData);
  };

  const handleEmploymentStatusChange = (
    value: UserProfile["employmentStatus"]
  ) => {
    const updatedData = { ...formData, employmentStatus: value };
    setFormData(updatedData);
    updateProfileInStore(updatedData);
  };

  const handleIndustryChange = (value: string) => {
    const updatedData = { ...formData, industry: value };
    setFormData(updatedData);
    updateProfileInStore(updatedData);
  };

  const handleTargetRoleChange = (value: string) => {
    const updatedData = { ...formData, targetRole: value };
    setFormData(updatedData);
    updateProfileInStore(updatedData);
  };

  const updateProfileInStore = (data: Partial<UserProfile>) => {
    // Only save to store if we have the required fields
    if (data.careerLevel && data.employmentStatus && data.industry) {
      const completeProfile: UserProfile = {
        careerLevel: data.careerLevel,
        employmentStatus: data.employmentStatus,
        industry: data.industry,
        targetRole: data.targetRole || "",
        yearsOfExperience: data.yearsOfExperience || 0,
        hasWorkExperience: data.hasWorkExperience || false,
        profileScore: calculateProfileScore(data as UserProfile),
      };
      setUserProfile(completeProfile);
    }
  };

  const calculateProfileScore = (profile: UserProfile): number => {
    let score = 0;

    // Base score for completing all fields
    score += 40;

    // Bonus for work experience
    if (profile.hasWorkExperience) {
      score += 20;
    }

    // Bonus for target role specificity
    if (profile.targetRole?.trim()) {
      score += 15;
    }

    // Bonus for industry specificity
    if (profile.industry && profile.industry !== "Other") {
      score += 15;
    }

    // Bonus for career progression alignment
    if (
      (profile.careerLevel === "entry-level" &&
        profile.yearsOfExperience <= 2) ||
      (profile.careerLevel === "mid-career" &&
        profile.yearsOfExperience >= 3 &&
        profile.yearsOfExperience <= 7) ||
      (profile.careerLevel === "senior" &&
        profile.yearsOfExperience >= 8 &&
        profile.yearsOfExperience <= 15) ||
      (profile.careerLevel === "executive" && profile.yearsOfExperience >= 15)
    ) {
      score += 10;
    }

    return Math.min(score, 100);
  };

  const getCompletionPercentage = (): number => {
    const requiredFields = ["careerLevel", "employmentStatus", "industry"];
    const completedFields = requiredFields.filter(
      (field) => formData[field as keyof UserProfile]
    ).length;

    return Math.round((completedFields / requiredFields.length) * 100);
  };

  const isFormComplete = getCompletionPercentage() === 100;

  return (
    <div className="space-y-8">
      {/* Enhanced Header with Better Background */}
      <div className="text-center space-y-6 relative">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-purple-50/30 to-indigo-50/50 rounded-3xl -z-10"></div>
        <div className="absolute top-4 left-4 w-20 h-20 bg-blue-200/30 rounded-full blur-xl"></div>
        <div className="absolute bottom-4 right-4 w-16 h-16 bg-purple-200/30 rounded-full blur-xl"></div>

        <div className="relative pt-8 pb-6">
          <div className="inline-flex items-center space-x-3 bg-gradient-to-r from-blue-100 to-purple-100 border border-blue-300 rounded-full px-8 py-3 shadow-lg mb-6">
            <Target className="h-6 w-6 text-blue-600" />
            <span className="text-xl font-bold text-blue-800">Profile Assessment</span>
          </div>
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Tell us about yourself
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Help us understand your professional background to recommend the best resume templates
          </p>
          <div className="mt-6">
            <div className="flex items-center justify-center space-x-3">
              <div className="w-full bg-gray-200 rounded-full h-3 max-w-sm">
                <div
                  className="bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full transition-all duration-500"
                  style={{ width: `${getCompletionPercentage()}%` }}
                />
              </div>
              <span className="text-lg font-semibold text-gray-700">
                {getCompletionPercentage()}%
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="grid gap-6">
        {/* Career Level Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Briefcase className="h-5 w-5" />
              <span>Career Level</span>
            </CardTitle>
            <CardDescription>
              Select the option that best describes your current career stage
            </CardDescription>
          </CardHeader>
          <CardContent>
            <RadioGroup
              value={formData.careerLevel || ""}
              onValueChange={handleCareerLevelChange}
              className="grid grid-cols-1 md:grid-cols-2 gap-4"
            >
              {careerLevels.map((level) => {
                const Icon = level.icon;
                return (
                  <div
                    key={level.value}
                    className="flex items-center space-x-2"
                  >
                    <RadioGroupItem value={level.value} id={level.value} />
                    <Label
                      htmlFor={level.value}
                      className={cn(
                        "flex items-center space-x-3 p-3 rounded-lg border cursor-pointer transition-colors flex-1",
                        formData.careerLevel === level.value
                          ? "border-blue-500 bg-blue-50"
                          : "border-gray-200 hover:border-gray-300"
                      )}
                    >
                      <Icon className="h-5 w-5 text-gray-600" />
                      <div>
                        <div className="font-medium">{level.label}</div>
                        <div className="text-sm text-gray-500">
                          {level.description}
                        </div>
                      </div>
                    </Label>
                  </div>
                );
              })}
            </RadioGroup>
          </CardContent>
        </Card>

        {/* Employment Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="h-5 w-5" />
              <span>Employment Status</span>
            </CardTitle>
            <CardDescription>
              What best describes your current situation?
            </CardDescription>
          </CardHeader>
          <CardContent>
            <RadioGroup
              value={formData.employmentStatus || ""}
              onValueChange={handleEmploymentStatusChange}
              className="grid grid-cols-1 md:grid-cols-2 gap-4"
            >
              {employmentStatuses.map((status) => (
                <div key={status.value} className="flex items-center space-x-2">
                  <RadioGroupItem value={status.value} id={status.value} />
                  <Label
                    htmlFor={status.value}
                    className={cn(
                      "flex items-center space-x-3 p-3 rounded-lg border cursor-pointer transition-colors flex-1",
                      formData.employmentStatus === status.value
                        ? "border-blue-500 bg-blue-50"
                        : "border-gray-200 hover:border-gray-300"
                    )}
                  >
                    <div>
                      <div className="font-medium">{status.label}</div>
                      <div className="text-sm text-gray-500">
                        {status.description}
                      </div>
                    </div>
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </CardContent>
        </Card>

        {/* Industry and Target Role */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Building className="h-5 w-5" />
                <span>Industry</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Select
                value={formData.industry || ""}
                onValueChange={handleIndustryChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select your industry" />
                </SelectTrigger>
                <SelectContent>
                  {commonIndustries.map((industry) => (
                    <SelectItem key={industry} value={industry}>
                      {industry}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Target className="h-5 w-5" />
                <span>Target Role</span>
                <Badge variant="secondary">Optional</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Input
                value={formData.targetRole || ""}
                onChange={(e) => handleTargetRoleChange(e.target.value)}
                placeholder="e.g., Senior Software Engineer, Marketing Manager"
              />
            </CardContent>
          </Card>
        </div>

        {/* Completion Status */}
        {isFormComplete && (
          <Card className="bg-green-50 border-green-200">
            <CardContent className="pt-6">
              <div className="flex items-center justify-center space-x-2 text-green-700">
                <CheckCircle className="h-5 w-5" />
                <span className="font-medium">
                  Profile Complete! You can now proceed to template selection.
                </span>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
