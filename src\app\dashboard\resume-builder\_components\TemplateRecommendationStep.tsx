"use client";

import { useEffect, useState } from "react";
import { useGlobalStore, TemplateRecommendation } from "@/store/useGlobalStore";
import { getTemplateRecommendations } from "./templateRecommendation";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TemplatePreview } from './templates/TemplateRenderer';
import { getSampleDataForCareerField } from './careerFieldSampleData';
import {
  Star,
  CheckCircle,
  FileText,
  Zap,
  Crown,
  Award,
  Target,
  Sparkles,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { motion } from "motion/react";

interface TemplateCardProps {
  recommendation: TemplateRecommendation;
  isSelected: boolean;
  onSelect: () => void;
}

function TemplateCard({
  recommendation,
  isSelected,
  onSelect,
}: TemplateCardProps) {
  const { resumeBuilder } = useGlobalStore();

  // Use live data if available, otherwise use sample data
  const hasUserData = resumeBuilder.data.personalInfo?.fullName ||
                     resumeBuilder.data.experience?.length > 0 ||
                     resumeBuilder.data.education?.length > 0;

  const previewData = hasUserData
    ? resumeBuilder.data
    : getSampleDataForCareerField(resumeBuilder.userProfile?.industry || 'entry-level');

  const template = {
    id: recommendation.templateId,
    name:
      recommendation.templateId.charAt(0).toUpperCase() +
      recommendation.templateId.slice(1),
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-emerald-700 bg-emerald-50 border-emerald-200";
    if (score >= 70) return "text-blue-700 bg-blue-50 border-blue-200";
    if (score >= 50) return "text-amber-700 bg-amber-50 border-amber-200";
    return "text-gray-700 bg-gray-50 border-gray-200";
  };

  const getScoreIcon = (score: number) => {
    if (score >= 90) return Crown;
    if (score >= 70) return Award;
    if (score >= 50) return Target;
    return Star;
  };

  const ScoreIcon = getScoreIcon(recommendation.suitabilityScore);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn(
        "relative group cursor-pointer transition-all duration-300",
        isSelected
          ? "ring-3 ring-blue-500 ring-offset-4 shadow-2xl scale-105"
          : "hover:shadow-xl hover:-translate-y-2 hover:scale-102"
      )}
      onClick={onSelect}
    >
      {/* Recommendation Badge */}
      {recommendation.isRecommended && (
        <div className="absolute -top-3 -right-3 z-10">
          <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white text-xs font-bold px-3 py-1 rounded-full shadow-lg flex items-center">
            <Crown className="h-3 w-3 mr-1" />
            RECOMMENDED
          </div>
        </div>
      )}

      <Card className={cn(
        "h-full overflow-hidden border-2 transition-all duration-300",
        isSelected
          ? "border-blue-500 bg-blue-50/30"
          : "border-gray-200 bg-white hover:border-blue-300"
      )}>
        {/* Template Preview - Larger and more prominent */}
        <div className="relative">
          <TemplatePreview
            template={recommendation.templateId as 'modern' | 'classic' | 'creative' | 'minimal'}
            sampleData={previewData}
            isSelected={isSelected}
            onClick={onSelect}
          />

          {/* Selection Overlay */}
          {isSelected && (
            <div className="absolute inset-0 bg-blue-500/10 border-2 border-blue-500 rounded-lg flex items-center justify-center">
              <div className="bg-blue-500 text-white rounded-full p-2 shadow-lg">
                <CheckCircle className="h-6 w-6" />
              </div>
            </div>
          )}
        </div>

        <CardContent className="p-4 space-y-3">
          {/* Header with Score */}
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-bold text-gray-900">{template.name}</h3>
            <div
              className={cn(
                "flex items-center space-x-1 px-3 py-1 rounded-full border text-sm font-bold",
                getScoreColor(recommendation.suitabilityScore)
              )}
            >
              <ScoreIcon className="h-4 w-4" />
              <span>{recommendation.suitabilityScore}%</span>
            </div>
          </div>

          {/* Key Benefits */}
          <div className="space-y-2">
            <h4 className="font-semibold text-sm text-gray-800 flex items-center">
              <Sparkles className="h-4 w-4 mr-1 text-blue-500" />
              Perfect for you because:
            </h4>
            <ul className="space-y-1">
              {recommendation.reasoning.slice(0, 2).map((reason, index) => (
                <li
                  key={index}
                  className="text-sm text-gray-600 flex items-start"
                >
                  <CheckCircle className="h-3 w-3 text-green-500 mt-0.5 mr-2 flex-shrink-0" />
                  {reason}
                </li>
              ))}
            </ul>
          </div>

          {/* Template Features */}
          <div className="flex flex-wrap gap-2">
            <Badge variant="outline" className="text-xs bg-green-50 border-green-200 text-green-700">
              <Zap className="h-3 w-3 mr-1" />
              ATS-Friendly
            </Badge>
            <Badge variant="outline" className="text-xs bg-blue-50 border-blue-200 text-blue-700">
              Professional
            </Badge>
          </div>

          {/* Action Button */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              onSelect();
            }}
            className={cn(
              "w-full py-2 px-4 rounded-lg font-medium transition-all duration-200",
              isSelected
                ? "bg-blue-500 text-white shadow-lg"
                : "bg-gray-100 text-gray-700 hover:bg-blue-500 hover:text-white"
            )}
          >
            {isSelected ? "Selected" : "Choose This Template"}
          </button>
        </CardContent>
      </Card>
    </motion.div>
  );
}

export function TemplateRecommendationStep() {
  const { resumeBuilder, setRecommendedTemplates, setResumeTemplate } =
    useGlobalStore();

  const { userProfile, recommendedTemplates, data } = resumeBuilder;
  const [isLoading, setIsLoading] = useState(false);

  // Use the template from global state directly
  const selectedTemplateId = data.template;

  // Generate recommendations when component mounts or profile changes
  useEffect(() => {
    if (userProfile) {
      setIsLoading(true);

      // Simulate API call delay for better UX
      setTimeout(() => {
        const recommendations = getTemplateRecommendations(
          userProfile,
          data.template
        );
        setRecommendedTemplates(recommendations);
        setIsLoading(false);
      }, 800);
    }
  }, [userProfile, data.template, setRecommendedTemplates]);

  const handleTemplateSelect = (templateId: string) => {
    // Ensure templateId is one of the valid template types
    const validTemplate = templateId as
      | "modern"
      | "classic"
      | "creative"
      | "minimal";
    setResumeTemplate(validTemplate);
  };

  if (!userProfile) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 mb-4">
          Please complete your profile assessment first.
        </div>
        <Button onClick={() => window.history.back()} variant="outline">
          Go Back to Profile Assessment
        </Button>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Finding perfect templates for you
          </h2>
          <p className="text-sm text-gray-600">
            Analyzing your profile to recommend the best resume templates...
          </p>
        </div>

        <div className="flex items-center justify-center py-12">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="text-gray-600">Generating recommendations...</span>
          </div>
        </div>

        {/* Loading skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="bg-gray-200 rounded-lg h-80"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  const topRecommendation = recommendedTemplates?.[0];
  const otherRecommendations = recommendedTemplates?.slice(1) || [];

  return (
    <div className="space-y-8">
      {/* Enhanced Header */}
      <div className="text-center space-y-4">
        <div className="space-y-2">
          <h2 className="text-3xl font-bold text-gray-900">
            Choose Your Perfect Template
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            We've analyzed your profile and selected templates that will make you stand out to employers
          </p>
        </div>

        {userProfile && (
          <div className="flex items-center justify-center space-x-4">
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-full px-4 py-2">
              <span className="text-sm font-medium text-blue-800">
                {userProfile.careerLevel.replace("-", " ")} • {userProfile.industry}
              </span>
            </div>
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-full px-4 py-2">
              <span className="text-sm font-medium text-green-800">
                {userProfile.profileScore}% Profile Match
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Top Recommendation - Featured */}
      {topRecommendation && (
        <div className="space-y-6">
          <div className="text-center">
            <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-yellow-100 to-orange-100 border border-yellow-300 rounded-full px-6 py-2 mb-4">
              <Crown className="h-5 w-5 text-yellow-600" />
              <span className="text-lg font-bold text-yellow-800">Best Match for You</span>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Featured Template Card */}
            <div className="relative">
              <TemplateCard
                recommendation={topRecommendation}
                isSelected={selectedTemplateId === topRecommendation.templateId}
                onSelect={() =>
                  handleTemplateSelect(topRecommendation.templateId)
                }
              />
              {/* Featured Badge */}
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white text-sm font-bold px-4 py-2 rounded-full shadow-lg">
                  ⭐ FEATURED
                </div>
              </div>
            </div>

            {/* Detailed Explanation */}
            <div className="space-y-6">
              <Card className="border-2 border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50">
                <CardHeader>
                  <CardTitle className="text-xl text-blue-900 flex items-center">
                    <Award className="h-6 w-6 mr-2" />
                    Why This Template is Perfect
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <ul className="space-y-3">
                    {topRecommendation.reasoning.map((reason, index) => (
                      <li
                        key={index}
                        className="text-blue-800 flex items-start"
                      >
                        <CheckCircle className="h-5 w-5 mr-3 mt-0.5 text-blue-600 flex-shrink-0" />
                        <span className="font-medium">{reason}</span>
                      </li>
                    ))}
                  </ul>

                  <div className="mt-6 p-4 bg-white/60 rounded-lg border border-blue-200">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-blue-800">Compatibility Score</span>
                      <span className="text-lg font-bold text-blue-900">{topRecommendation.suitabilityScore}%</span>
                    </div>
                    <div className="mt-2 w-full bg-blue-200 rounded-full h-3">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full transition-all duration-1000"
                        style={{ width: `${topRecommendation.suitabilityScore}%` }}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Action */}
              <button
                onClick={() => handleTemplateSelect(topRecommendation.templateId)}
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-4 px-6 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
              >
                Choose This Template
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Other Recommendations */}
      {otherRecommendations.length > 0 && (
        <div className="space-y-8">
          <div className="text-center space-y-3">
            <h3 className="text-2xl font-bold text-gray-900">
              More Great Options
            </h3>
            <p className="text-lg text-gray-600 max-w-xl mx-auto">
              These templates also work well for your profile and career goals
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {otherRecommendations.map((recommendation, index) => (
              <motion.div
                key={recommendation.templateId}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <TemplateCard
                  recommendation={recommendation}
                  isSelected={selectedTemplateId === recommendation.templateId}
                  onSelect={() => handleTemplateSelect(recommendation.templateId)}
                />
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Selected Template Confirmation */}
      {selectedTemplateId && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <Card className="bg-gradient-to-r from-green-50 to-emerald-50 border-2 border-green-300 shadow-lg">
            <CardContent className="pt-6 pb-6">
              <div className="text-center space-y-4">
                <div className="flex items-center justify-center">
                  <div className="bg-green-500 text-white rounded-full p-3">
                    <CheckCircle className="h-8 w-8" />
                  </div>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-green-800 mb-2">
                    Perfect Choice!
                  </h3>
                  <p className="text-green-700 font-medium">
                    <span className="font-bold">
                      {selectedTemplateId.charAt(0).toUpperCase() +
                        selectedTemplateId.slice(1)}
                    </span>{" "}
                    template selected. You're ready to build an amazing resume!
                  </p>
                </div>
                <div className="bg-white/60 rounded-lg p-4 border border-green-200">
                  <p className="text-sm text-green-600">
                    ✨ Your template is optimized for ATS systems and designed to impress recruiters
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  );
}
